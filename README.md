# React Vite Router + Node.js Full-Stack Demo

A modern full-stack web application showcasing **Vite**, **React Router DOM**, and **Node.js** integration. This project demonstrates client-side routing, API integration, and modern development practices.

## 🚀 Features

### Frontend (React + Vite)
- ⚡ **Vite** for lightning-fast development with instant HMR
- 🛣️ **React Router DOM v6** with nested routes and dynamic parameters
- 📱 **Responsive Design** with modern CSS Grid and Flexbox
- 🎣 **React Hooks** (useState, useEffect, useParams, useNavigate, useLocation)
- 🔗 **API Integration** with Axios for HTTP requests
- 🎨 **Modern UI** with CSS custom properties and smooth animations

### Backend (Node.js + Express)
- 🟢 **Node.js** with ES modules support
- 🚀 **Express.js** RESTful API with multiple endpoints
- 🌐 **CORS** enabled for cross-origin requests
- ✅ **Request validation** and comprehensive error handling
- 📊 **Multiple data endpoints** (users, products, contacts, stats)
- 🔍 **Health monitoring** and API documentation

## 📁 Project Structure

```
project/
├── client/                 # Vite React Frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   │   ├── Layout.jsx
│   │   │   ├── Navbar.jsx
│   │   │   └── Footer.jsx
│   │   ├── pages/          # Route components
│   │   │   ├── Home.jsx
│   │   │   ├── About.jsx
│   │   │   ├── Products.jsx
│   │   │   ├── ProductDetail.jsx
│   │   │   ├── Users.jsx
│   │   │   ├── UserProfile.jsx
│   │   │   ├── Contact.jsx
│   │   │   └── NotFound.jsx
│   │   ├── App.jsx         # Main app with router setup
│   │   ├── App.css         # Component styles
│   │   ├── index.css       # Global styles
│   │   └── main.jsx        # Entry point
│   ├── index.html          # HTML template
│   ├── vite.config.js      # Vite configuration
│   └── package.json
├── server/                 # Node.js Express Backend
│   ├── index.js            # Express server with API routes
│   ├── .env                # Environment variables
│   └── package.json
├── package.json            # Root package with scripts
└── README.md
```

## 🛠️ Technology Stack

### Frontend
- **Vite** - Next generation frontend tooling
- **React 18** - Latest React with concurrent features
- **React Router DOM v6** - Declarative routing for React
- **Axios** - Promise-based HTTP client
- **CSS3** - Modern styling with custom properties

### Backend
- **Node.js** - JavaScript runtime with ES modules
- **Express.js** - Fast, unopinionated web framework
- **CORS** - Cross-Origin Resource Sharing middleware
- **dotenv** - Environment variable management

### Development Tools
- **Concurrently** - Run multiple commands simultaneously
- **Nodemon** - Auto-restart server during development

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Install all dependencies (root, client, and server)
npm run install-all

# Or install separately
npm install              # Root dependencies
npm run install-client  # Client dependencies
npm run install-server  # Server dependencies
```

### 2. Start Development
```bash
# Start both client and server concurrently
npm run dev

# Or start separately
npm run client  # Vite dev server on http://localhost:3000
npm run server  # Express server on http://localhost:5000
```

### 3. Build for Production
```bash
npm run build    # Build client for production
npm run preview  # Preview production build
```

## 🎯 Learning Objectives

### React Router DOM Concepts

#### 1. **BrowserRouter Setup**
```jsx
import { BrowserRouter as Router } from 'react-router-dom';

function App() {
  return (
    <Router>
      <Routes>
        {/* Routes here */}
      </Routes>
    </Router>
  );
}
```

#### 2. **Nested Routes with Layout**
```jsx
<Route path="/" element={<Layout />}>
  <Route index element={<Home />} />
  <Route path="about" element={<About />} />
  <Route path="products" element={<Products />} />
</Route>
```

#### 3. **Dynamic Routes with Parameters**
```jsx
<Route path="products/:id" element={<ProductDetail />} />
<Route path="users/:id" element={<UserProfile />} />
```

#### 4. **Navigation Hooks**
```jsx
import { useNavigate, useParams, useLocation } from 'react-router-dom';

function Component() {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  
  // Programmatic navigation
  const goBack = () => navigate(-1);
}
```

#### 5. **Link Navigation**
```jsx
import { Link } from 'react-router-dom';

<Link to="/products">Products</Link>
<Link to={`/products/${product.id}`}>View Details</Link>
```

### Vite Benefits Demonstrated

#### ⚡ **Instant Server Start**
- Dev server starts immediately regardless of project size
- No bundling during development

#### 🔥 **Hot Module Replacement (HMR)**
- Instant updates without losing application state
- Fast refresh for React components

#### 📦 **Optimized Builds**
- Pre-configured Rollup for production builds
- Automatic code splitting and optimization

#### 🔧 **Zero Configuration**
- Works out of the box with sensible defaults
- Easy to extend and customize

## 📡 API Endpoints

### Users
- `GET /api/users` - Get all users (with optional role filter)
- `GET /api/users/:id` - Get specific user by ID

### Products
- `GET /api/products` - Get all products (with filtering options)
- `GET /api/products/:id` - Get specific product by ID

### Contact
- `POST /api/contact` - Submit contact form
- `GET /api/contacts` - Get all contact submissions

### System
- `GET /api/health` - Health check endpoint
- `GET /api/stats` - Get application statistics
- `GET /api` - API information and available endpoints

## 🎨 Key Features Demonstrated

### 1. **Client-Side Routing**
- Multiple pages without full page reloads
- URL synchronization with application state
- Browser history management
- 404 error handling

### 2. **API Integration**
- Fetch data from Node.js backend
- Handle loading and error states
- Real-time filtering and search
- Form submission with validation

### 3. **Modern React Patterns**
- Functional components with hooks
- State management with useState
- Side effects with useEffect
- Custom hooks for reusable logic

### 4. **Responsive Design**
- Mobile-first approach
- CSS Grid and Flexbox layouts
- Responsive navigation with mobile menu
- Adaptive component layouts

### 5. **Development Experience**
- Instant feedback with Vite HMR
- Concurrent development servers
- Environment-based configuration
- Comprehensive error handling

## 🔧 Development Scripts

```bash
# Development
npm run dev          # Start both client and server
npm run client       # Start only Vite dev server
npm run server       # Start only Express server

# Production
npm run build        # Build client for production
npm run preview      # Preview production build
npm start           # Start production server

# Installation
npm run install-all     # Install all dependencies
npm run install-client  # Install client dependencies
npm run install-server  # Install server dependencies
```

## 🌟 Next Steps

To extend this project, consider adding:

- **State Management**: Redux Toolkit or Zustand for complex state
- **Authentication**: JWT-based auth with protected routes
- **Database Integration**: MongoDB, PostgreSQL, or SQLite
- **Testing**: Jest, React Testing Library, Cypress
- **TypeScript**: Add type safety to both client and server
- **Deployment**: Docker, Vercel, Netlify, or Heroku
- **Real-time Features**: WebSockets with Socket.io
- **Progressive Web App**: Service workers and offline support

## 📚 Learning Resources

- [Vite Documentation](https://vitejs.dev/)
- [React Router Documentation](https://reactrouter.com/)
- [React Hooks Guide](https://reactjs.org/docs/hooks-intro.html)
- [Express.js Guide](https://expressjs.com/)
- [Modern JavaScript Features](https://developer.mozilla.org/en-US/docs/Web/JavaScript)

## 🤝 Contributing

This project is designed for learning purposes. Feel free to:
- Fork the repository
- Experiment with new features
- Submit improvements
- Share your learning experience

---

**Happy coding!** 🚀 This project demonstrates modern full-stack development with some of the best tools available today.

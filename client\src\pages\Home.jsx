import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

function Home() {
  const [apiStatus, setApiStatus] = useState('checking...');
  const [stats, setStats] = useState({
    users: 0,
    products: 0,
    contacts: 0,
    categories: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check API health and get basic stats
    const checkApiHealth = async () => {
      try {
        const [healthResponse, statsResponse] = await Promise.all([
          axios.get('/api/health'),
          axios.get('/api/stats')
        ]);
        
        if (healthResponse.data.status === 'OK') {
          setApiStatus('✅ Connected');
          setStats(statsResponse.data.data);
        }
      } catch (error) {
        setApiStatus('❌ Disconnected');
        console.error('API connection failed:', error);
      } finally {
        setLoading(false);
      }
    };

    checkApiHealth();
  }, []);

  return (
    <div className="page home-page">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>⚡ Welcome to Vite React Router Demo</h1>
          <p className="hero-subtitle">
            A modern full-stack application showcasing Vite, React Router DOM, and Node.js
          </p>
          
          {loading ? (
            <div className="loading-stats">Loading stats...</div>
          ) : (
            <div className="hero-stats">
              <div className="stat-item">
                <span className="stat-number">{stats.users}</span>
                <span className="stat-label">Users</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">{stats.products}</span>
                <span className="stat-label">Products</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">{stats.contacts}</span>
                <span className="stat-label">Messages</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">{stats.categories}</span>
                <span className="stat-label">Categories</span>
              </div>
            </div>
          )}
          
          <div className="api-status">
            <strong>API Status:</strong> {apiStatus}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <h2>🎯 Key Features</h2>
        <div className="features-grid">
          <div className="feature-card">
            <div className="feature-icon">⚡</div>
            <h3>Vite Development</h3>
            <p>Lightning-fast development with Vite's instant HMR</p>
            <ul>
              <li>Instant server start</li>
              <li>Hot Module Replacement</li>
              <li>Optimized builds</li>
              <li>ES modules support</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">🛣️</div>
            <h3>React Router DOM</h3>
            <p>Client-side routing with modern React Router</p>
            <ul>
              <li>Nested routes</li>
              <li>Dynamic parameters</li>
              <li>Layout components</li>
              <li>Navigation hooks</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">🟢</div>
            <h3>Node.js Backend</h3>
            <p>RESTful API server with Express.js</p>
            <ul>
              <li>ES modules support</li>
              <li>CORS configuration</li>
              <li>Multiple endpoints</li>
              <li>Error handling</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Quick Navigation */}
      <section className="quick-nav">
        <h2>🧭 Explore the Application</h2>
        <div className="nav-cards">
          <Link to="/about" className="nav-card">
            <div className="nav-icon">ℹ️</div>
            <h3>About</h3>
            <p>Learn about the project architecture and technologies used</p>
          </Link>

          <Link to="/products" className="nav-card">
            <div className="nav-icon">📦</div>
            <h3>Products</h3>
            <p>Browse products with search, filtering, and detailed views</p>
          </Link>

          <Link to="/users" className="nav-card">
            <div className="nav-icon">👥</div>
            <h3>Users</h3>
            <p>View user profiles with dynamic routing</p>
          </Link>

          <Link to="/contact" className="nav-card">
            <div className="nav-icon">📧</div>
            <h3>Contact</h3>
            <p>Send messages through our contact form with API integration</p>
          </Link>
        </div>
      </section>

      {/* Technology Highlights */}
      <section className="tech-highlights">
        <h2>🛠️ Technology Stack</h2>
        <div className="tech-grid">
          <div className="tech-item">
            <div className="tech-icon">⚡</div>
            <h3>Vite</h3>
            <p>Next generation frontend tooling with instant dev server</p>
          </div>
          <div className="tech-item">
            <div className="tech-icon">⚛️</div>
            <h3>React 18</h3>
            <p>Latest React with concurrent features and hooks</p>
          </div>
          <div className="tech-item">
            <div className="tech-icon">🛣️</div>
            <h3>React Router</h3>
            <p>Declarative routing for React applications</p>
          </div>
          <div className="tech-item">
            <div className="tech-icon">🟢</div>
            <h3>Node.js</h3>
            <p>JavaScript runtime for building scalable server applications</p>
          </div>
        </div>
      </section>

      {/* Getting Started */}
      <section className="getting-started">
        <h2>🚀 Getting Started</h2>
        <div className="steps">
          <div className="step">
            <div className="step-number">1</div>
            <div className="step-content">
              <h3>Install Dependencies</h3>
              <code>npm run install-all</code>
            </div>
          </div>
          <div className="step">
            <div className="step-number">2</div>
            <div className="step-content">
              <h3>Start Development</h3>
              <code>npm run dev</code>
            </div>
          </div>
          <div className="step">
            <div className="step-number">3</div>
            <div className="step-content">
              <h3>Explore Features</h3>
              <p>Navigate through different pages and see routing in action!</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Home;

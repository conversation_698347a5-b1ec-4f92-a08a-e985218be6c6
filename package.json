{"name": "react-router-nodejs-fullstack", "version": "1.0.0", "description": "Full-stack application with React Router DOM and Node.js server", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm run install-server && npm run install-client", "build": "cd client && npm run build", "start": "cd server && npm start"}, "keywords": ["react", "react-router", "nodejs", "express", "fullstack"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0"}}
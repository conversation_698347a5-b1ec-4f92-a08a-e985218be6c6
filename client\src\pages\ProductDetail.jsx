import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';

function ProductDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchProduct();
  }, [id]);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/products/${id}`);
      setProduct(response.data.data);
      setError(null);
    } catch (err) {
      setError(err.response?.status === 404 ? 'Product not found' : 'Failed to fetch product');
      console.error('Error fetching product:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="page product-detail-page">
        <div className="loading">
          <div className="spinner"></div>
          <p>Loading product...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page product-detail-page">
        <div className="error">
          <h2>❌ Error</h2>
          <p>{error}</p>
          <div className="error-actions">
            <button onClick={fetchProduct} className="retry-button">
              Try Again
            </button>
            <Link to="/products" className="back-button">
              Back to Products
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page product-detail-page">
      {/* Breadcrumb */}
      <nav className="breadcrumb">
        <Link to="/">Home</Link>
        <span>/</span>
        <Link to="/products">Products</Link>
        <span>/</span>
        <span>{product.name}</span>
      </nav>

      {/* Product Detail */}
      <section className="product-detail">
        <div className="product-image-large">
          <div className="product-icon-large">
            {product.image}
          </div>
        </div>

        <div className="product-info-detailed">
          <h1>{product.name}</h1>
          <p className="product-category-badge">{product.category}</p>
          
          <div className="product-price-large">
            ${product.price.toFixed(2)}
          </div>

          <div className="product-stock-info">
            {product.stock > 0 ? (
              <div className="in-stock-large">
                <span className="stock-icon">✅</span>
                <span>In Stock ({product.stock} available)</span>
              </div>
            ) : (
              <div className="out-of-stock-large">
                <span className="stock-icon">❌</span>
                <span>Out of Stock</span>
              </div>
            )}
          </div>

          <div className="product-description-detailed">
            <h3>Description</h3>
            <p>{product.description}</p>
          </div>

          <div className="product-actions-detailed">
            <button 
              className="add-to-cart-btn"
              disabled={product.stock === 0}
            >
              {product.stock > 0 ? 'Add to Cart' : 'Out of Stock'}
            </button>
            <button className="wishlist-btn">
              ❤️ Add to Wishlist
            </button>
          </div>

          <div className="product-meta">
            <div className="meta-item">
              <strong>Product ID:</strong> {product.id}
            </div>
            <div className="meta-item">
              <strong>Category:</strong> {product.category}
            </div>
            <div className="meta-item">
              <strong>Availability:</strong> {product.stock > 0 ? 'In Stock' : 'Out of Stock'}
            </div>
          </div>
        </div>
      </section>

      {/* Navigation */}
      <section className="product-navigation">
        <button onClick={() => navigate(-1)} className="back-btn">
          ← Go Back
        </button>
        <Link to="/products" className="all-products-btn">
          View All Products
        </Link>
      </section>

      {/* Demo Info */}
      <section className="demo-info">
        <h2>🎯 React Router Features</h2>
        <div className="demo-features">
          <div className="demo-feature">
            <h3>useParams Hook</h3>
            <p>Extracting the product ID ({id}) from the URL parameter</p>
          </div>
          <div className="demo-feature">
            <h3>useNavigate Hook</h3>
            <p>Programmatic navigation with the "Go Back" button</p>
          </div>
          <div className="demo-feature">
            <h3>Dynamic API Calls</h3>
            <p>Fetching specific product data based on the route parameter</p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default ProductDetail;

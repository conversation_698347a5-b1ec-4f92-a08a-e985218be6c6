import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

function Products() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    category: '',
    search: '',
    minPrice: '',
    maxPrice: ''
  });

  // Fetch products from API
  useEffect(() => {
    fetchProducts();
  }, [filters]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await axios.get(`/api/products?${params}`);
      setProducts(response.data.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch products');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      category: '',
      search: '',
      minPrice: '',
      maxPrice: ''
    });
  };

  if (loading) {
    return (
      <div className="page products-page">
        <div className="loading">
          <div className="spinner"></div>
          <p>Loading products...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page products-page">
        <div className="error">
          <h2>❌ Error</h2>
          <p>{error}</p>
          <button onClick={fetchProducts} className="retry-button">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="page products-page">
      {/* Header */}
      <section className="page-header">
        <h1>📦 Products</h1>
        <p>Browse our collection with advanced filtering and search</p>
      </section>

      {/* Filters */}
      <section className="filters-section">
        <h2>🔍 Filter & Search</h2>
        <div className="filters">
          <div className="filter-group">
            <label htmlFor="search">Search:</label>
            <input
              type="text"
              id="search"
              placeholder="Search products..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>

          <div className="filter-group">
            <label htmlFor="category">Category:</label>
            <select
              id="category"
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
            >
              <option value="">All Categories</option>
              <option value="Electronics">Electronics</option>
              <option value="Books">Books</option>
              <option value="Accessories">Accessories</option>
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="minPrice">Min Price:</label>
            <input
              type="number"
              id="minPrice"
              placeholder="0"
              value={filters.minPrice}
              onChange={(e) => handleFilterChange('minPrice', e.target.value)}
            />
          </div>

          <div className="filter-group">
            <label htmlFor="maxPrice">Max Price:</label>
            <input
              type="number"
              id="maxPrice"
              placeholder="2000"
              value={filters.maxPrice}
              onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
            />
          </div>

          <button onClick={clearFilters} className="clear-filters">
            Clear All
          </button>
        </div>

        <div className="results-info">
          <p>Found {products.length} product{products.length !== 1 ? 's' : ''}</p>
        </div>
      </section>

      {/* Products Grid */}
      <section className="products-grid">
        {products.length === 0 ? (
          <div className="no-products">
            <h3>No products found</h3>
            <p>Try adjusting your search criteria or filters.</p>
          </div>
        ) : (
          products.map(product => (
            <div key={product.id} className="product-card">
              <div className="product-image">
                <div className="product-icon">
                  {product.image}
                </div>
              </div>
              
              <div className="product-info">
                <h3 className="product-name">{product.name}</h3>
                <p className="product-category">{product.category}</p>
                <p className="product-description">{product.description}</p>
                <div className="product-price">
                  ${product.price.toFixed(2)}
                </div>
                <div className="product-stock">
                  {product.stock > 0 ? (
                    <span className="in-stock">✅ In Stock ({product.stock})</span>
                  ) : (
                    <span className="out-of-stock">❌ Out of Stock</span>
                  )}
                </div>
              </div>
              
              <div className="product-actions">
                <Link 
                  to={`/products/${product.id}`} 
                  className="view-details-btn"
                >
                  View Details
                </Link>
              </div>
            </div>
          ))
        )}
      </section>

      {/* Demo Info */}
      <section className="demo-info">
        <h2>🎯 Features Demonstrated</h2>
        <div className="demo-features">
          <div className="demo-feature">
            <h3>🛣️ Dynamic Routing</h3>
            <p>Click "View Details" to navigate to /products/:id with URL parameters</p>
          </div>
          <div className="demo-feature">
            <h3>🔗 API Integration</h3>
            <p>Real-time filtering with backend API calls using Axios</p>
          </div>
          <div className="demo-feature">
            <h3>⚡ Vite HMR</h3>
            <p>Instant updates during development with Vite's Hot Module Replacement</p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Products;

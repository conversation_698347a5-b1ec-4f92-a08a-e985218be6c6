const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Sample data (in a real app, this would be a database)
let users = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'User' },
  { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'User' }
];

let products = [
  { id: 1, name: 'Laptop', price: 999.99, category: 'Electronics', stock: 50 },
  { id: 2, name: 'Smartphone', price: 699.99, category: 'Electronics', stock: 100 },
  { id: 3, name: 'Headphones', price: 199.99, category: 'Electronics', stock: 75 },
  { id: 4, name: 'Book', price: 29.99, category: 'Education', stock: 200 }
];

let contacts = [];

// Routes

// Home route
app.get('/api', (req, res) => {
  res.json({ 
    message: 'Welcome to React Router + Node.js API!',
    endpoints: [
      'GET /api/users - Get all users',
      'GET /api/users/:id - Get user by ID',
      'GET /api/products - Get all products',
      'GET /api/products/:id - Get product by ID',
      'POST /api/contact - Submit contact form',
      'GET /api/contact - Get all contact submissions'
    ]
  });
});

// Users routes
app.get('/api/users', (req, res) => {
  res.json({
    success: true,
    data: users,
    count: users.length
  });
});

app.get('/api/users/:id', (req, res) => {
  const userId = parseInt(req.params.id);
  const user = users.find(u => u.id === userId);
  
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }
  
  res.json({
    success: true,
    data: user
  });
});

// Products routes
app.get('/api/products', (req, res) => {
  const { category, minPrice, maxPrice } = req.query;
  let filteredProducts = [...products];
  
  if (category) {
    filteredProducts = filteredProducts.filter(p => 
      p.category.toLowerCase() === category.toLowerCase()
    );
  }
  
  if (minPrice) {
    filteredProducts = filteredProducts.filter(p => p.price >= parseFloat(minPrice));
  }
  
  if (maxPrice) {
    filteredProducts = filteredProducts.filter(p => p.price <= parseFloat(maxPrice));
  }
  
  res.json({
    success: true,
    data: filteredProducts,
    count: filteredProducts.length,
    filters: { category, minPrice, maxPrice }
  });
});

app.get('/api/products/:id', (req, res) => {
  const productId = parseInt(req.params.id);
  const product = products.find(p => p.id === productId);
  
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }
  
  res.json({
    success: true,
    data: product
  });
});

// Contact routes
app.post('/api/contact', (req, res) => {
  const { name, email, message } = req.body;
  
  if (!name || !email || !message) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }
  
  const newContact = {
    id: contacts.length + 1,
    name,
    email,
    message,
    timestamp: new Date().toISOString()
  };
  
  contacts.push(newContact);
  
  res.status(201).json({
    success: true,
    message: 'Contact form submitted successfully',
    data: newContact
  });
});

app.get('/api/contact', (req, res) => {
  res.json({
    success: true,
    data: contacts,
    count: contacts.length
  });
});

// Health check route
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 404 handler
app.use('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📡 API available at http://localhost:${PORT}/api`);
  console.log(`🔍 Health check: http://localhost:${PORT}/api/health`);
});

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Sample data (in a real app, this would be a database)
let users = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', avatar: '👨‍💼' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Developer', avatar: '👩‍💻' },
  { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Designer', avatar: '👨‍🎨' },
  { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'Manager', avatar: '👩‍💼' }
];

let products = [
  { 
    id: 1, 
    name: 'MacBook Pro', 
    price: 1299.99, 
    category: 'Electronics', 
    stock: 25,
    description: 'Powerful laptop for professionals',
    image: '💻'
  },
  { 
    id: 2, 
    name: 'iPhone 15', 
    price: 999.99, 
    category: 'Electronics', 
    stock: 50,
    description: 'Latest smartphone with advanced features',
    image: '📱'
  },
  { 
    id: 3, 
    name: 'AirPods Pro', 
    price: 249.99, 
    category: 'Electronics', 
    stock: 100,
    description: 'Wireless earbuds with noise cancellation',
    image: '🎧'
  },
  { 
    id: 4, 
    name: 'React Handbook', 
    price: 39.99, 
    category: 'Books', 
    stock: 200,
    description: 'Complete guide to React development',
    image: '📚'
  },
  { 
    id: 5, 
    name: 'Wireless Mouse', 
    price: 79.99, 
    category: 'Accessories', 
    stock: 75,
    description: 'Ergonomic wireless mouse',
    image: '🖱️'
  }
];

let contacts = [];

// Routes

// Home/API info route
app.get('/api', (req, res) => {
  res.json({ 
    message: '🚀 Welcome to React Vite + Node.js API!',
    version: '1.0.0',
    endpoints: {
      users: 'GET /api/users - Get all users',
      userById: 'GET /api/users/:id - Get user by ID',
      products: 'GET /api/products - Get all products',
      productById: 'GET /api/products/:id - Get product by ID',
      contact: 'POST /api/contact - Submit contact form',
      contacts: 'GET /api/contacts - Get all contact submissions',
      health: 'GET /api/health - Health check'
    },
    timestamp: new Date().toISOString()
  });
});

// Users routes
app.get('/api/users', (req, res) => {
  const { role } = req.query;
  let filteredUsers = [...users];
  
  if (role) {
    filteredUsers = filteredUsers.filter(u => 
      u.role.toLowerCase() === role.toLowerCase()
    );
  }
  
  res.json({
    success: true,
    data: filteredUsers,
    count: filteredUsers.length,
    filters: { role }
  });
});

app.get('/api/users/:id', (req, res) => {
  const userId = parseInt(req.params.id);
  const user = users.find(u => u.id === userId);
  
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }
  
  res.json({
    success: true,
    data: user
  });
});

// Products routes
app.get('/api/products', (req, res) => {
  const { category, minPrice, maxPrice, search } = req.query;
  let filteredProducts = [...products];
  
  if (category) {
    filteredProducts = filteredProducts.filter(p => 
      p.category.toLowerCase() === category.toLowerCase()
    );
  }
  
  if (minPrice) {
    filteredProducts = filteredProducts.filter(p => p.price >= parseFloat(minPrice));
  }
  
  if (maxPrice) {
    filteredProducts = filteredProducts.filter(p => p.price <= parseFloat(maxPrice));
  }
  
  if (search) {
    filteredProducts = filteredProducts.filter(p => 
      p.name.toLowerCase().includes(search.toLowerCase()) ||
      p.description.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  res.json({
    success: true,
    data: filteredProducts,
    count: filteredProducts.length,
    filters: { category, minPrice, maxPrice, search }
  });
});

app.get('/api/products/:id', (req, res) => {
  const productId = parseInt(req.params.id);
  const product = products.find(p => p.id === productId);
  
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }
  
  res.json({
    success: true,
    data: product
  });
});

// Contact routes
app.post('/api/contact', (req, res) => {
  const { name, email, subject, message } = req.body;
  
  if (!name || !email || !subject || !message) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }
  
  const newContact = {
    id: contacts.length + 1,
    name,
    email,
    subject,
    message,
    timestamp: new Date().toISOString(),
    status: 'new'
  };
  
  contacts.push(newContact);
  
  res.status(201).json({
    success: true,
    message: 'Contact form submitted successfully! We will get back to you soon.',
    data: newContact
  });
});

app.get('/api/contacts', (req, res) => {
  res.json({
    success: true,
    data: contacts,
    count: contacts.length
  });
});

// Health check route
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Server is running smoothly',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime()),
    memory: process.memoryUsage(),
    version: process.version
  });
});

// Stats route
app.get('/api/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      users: users.length,
      products: products.length,
      contacts: contacts.length,
      categories: [...new Set(products.map(p => p.category))].length
    }
  });
});

// 404 handler for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.path
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📡 API available at http://localhost:${PORT}/api`);
  console.log(`🔍 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📊 Stats: http://localhost:${PORT}/api/stats`);
});

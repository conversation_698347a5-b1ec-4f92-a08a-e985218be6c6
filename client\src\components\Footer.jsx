import React from 'react';
import { Link } from 'react-router-dom';

function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="footer">
      <div className="footer-container">
        <div className="footer-content">
          {/* Brand section */}
          <div className="footer-section">
            <h3>⚡ Vite React Router</h3>
            <p>A modern full-stack application built with Vite, React Router DOM, and Node.js.</p>
          </div>

          {/* Quick links */}
          <div className="footer-section">
            <h4>Quick Links</h4>
            <ul className="footer-links">
              <li><Link to="/">Home</Link></li>
              <li><Link to="/about">About</Link></li>
              <li><Link to="/products">Products</Link></li>
              <li><Link to="/users">Users</Link></li>
              <li><Link to="/contact">Contact</Link></li>
            </ul>
          </div>

          {/* Technologies */}
          <div className="footer-section">
            <h4>Built With</h4>
            <ul className="footer-tech">
              <li>⚡ Vite</li>
              <li>⚛️ React 18</li>
              <li>🛣️ React Router DOM</li>
              <li>🟢 Node.js</li>
              <li>🚀 Express.js</li>
            </ul>
          </div>

          {/* Contact info */}
          <div className="footer-section">
            <h4>Development</h4>
            <div className="footer-contact">
              <p>🌐 Frontend: localhost:3000</p>
              <p>🔗 Backend: localhost:5000</p>
              <p>📡 API: /api</p>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="footer-bottom">
          <p>&copy; {currentYear} Vite React Router Demo. Built for learning purposes.</p>
          <p>Made with ❤️ using Vite + React Router DOM + Node.js</p>
        </div>
      </div>
    </footer>
  );
}

export default Footer;

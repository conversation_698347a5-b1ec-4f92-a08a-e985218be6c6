import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

function Users() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [roleFilter, setRoleFilter] = useState('');

  useEffect(() => {
    fetchUsers();
  }, [roleFilter]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = roleFilter ? `?role=${roleFilter}` : '';
      const response = await axios.get(`/api/users${params}`);
      setUsers(response.data.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const roles = ['Admin', 'Developer', 'Designer', 'Manager'];

  if (loading) {
    return (
      <div className="page users-page">
        <div className="loading">
          <div className="spinner"></div>
          <p>Loading users...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page users-page">
        <div className="error">
          <h2>❌ Error</h2>
          <p>{error}</p>
          <button onClick={fetchUsers} className="retry-button">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="page users-page">
      {/* Header */}
      <section className="page-header">
        <h1>👥 Users</h1>
        <p>Browse user profiles and view detailed information</p>
      </section>

      {/* Filters */}
      <section className="filters-section">
        <h2>🔍 Filter by Role</h2>
        <div className="role-filters">
          <button 
            className={`role-filter ${roleFilter === '' ? 'active' : ''}`}
            onClick={() => setRoleFilter('')}
          >
            All Roles ({users.length})
          </button>
          {roles.map(role => (
            <button 
              key={role}
              className={`role-filter ${roleFilter === role ? 'active' : ''}`}
              onClick={() => setRoleFilter(role)}
            >
              {role}
            </button>
          ))}
        </div>
      </section>

      {/* Users Grid */}
      <section className="users-grid">
        {users.length === 0 ? (
          <div className="no-users">
            <h3>No users found</h3>
            <p>Try selecting a different role filter.</p>
          </div>
        ) : (
          users.map(user => (
            <div key={user.id} className="user-card">
              <div className="user-avatar">
                <span className="avatar-icon">{user.avatar}</span>
              </div>
              
              <div className="user-info">
                <h3 className="user-name">{user.name}</h3>
                <p className="user-email">{user.email}</p>
                <span className={`user-role role-${user.role.toLowerCase()}`}>
                  {user.role}
                </span>
              </div>
              
              <div className="user-actions">
                <Link 
                  to={`/users/${user.id}`} 
                  className="view-profile-btn"
                >
                  View Profile
                </Link>
              </div>
            </div>
          ))
        )}
      </section>

      {/* Demo Info */}
      <section className="demo-info">
        <h2>🎯 Features Demonstrated</h2>
        <div className="demo-features">
          <div className="demo-feature">
            <h3>🛣️ Dynamic Routing</h3>
            <p>Click "View Profile" to navigate to individual user pages</p>
          </div>
          <div className="demo-feature">
            <h3>🔍 API Filtering</h3>
            <p>Filter users by role with real-time API calls</p>
          </div>
          <div className="demo-feature">
            <h3>📱 Responsive Cards</h3>
            <p>User cards adapt to different screen sizes</p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Users;

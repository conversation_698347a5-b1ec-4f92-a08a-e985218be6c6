import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

function Home() {
  const [apiStatus, setApiStatus] = useState('checking...');
  const [stats, setStats] = useState({
    users: 0,
    products: 0,
    contacts: 0
  });

  useEffect(() => {
    // Check API health and get basic stats
    const checkApiHealth = async () => {
      try {
        const healthResponse = await axios.get('/api/health');
        if (healthResponse.data.status === 'OK') {
          setApiStatus('✅ Connected');
          
          // Get stats
          const [usersRes, productsRes, contactsRes] = await Promise.all([
            axios.get('/api/users'),
            axios.get('/api/products'),
            axios.get('/api/contact')
          ]);
          
          setStats({
            users: usersRes.data.count,
            products: productsRes.data.count,
            contacts: contactsRes.data.count
          });
        }
      } catch (error) {
        setApiStatus('❌ Disconnected');
        console.error('API connection failed:', error);
      }
    };

    checkApiHealth();
  }, []);

  return (
    <div className="page home-page">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>🚀 Welcome to React Router Demo</h1>
          <p className="hero-subtitle">
            A full-stack application showcasing React Router DOM with Node.js backend
          </p>
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">{stats.users}</span>
              <span className="stat-label">Users</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{stats.products}</span>
              <span className="stat-label">Products</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{stats.contacts}</span>
              <span className="stat-label">Messages</span>
            </div>
          </div>
          <div className="api-status">
            <strong>API Status:</strong> {apiStatus}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <h2>🎯 What You'll Learn</h2>
        <div className="features-grid">
          <div className="feature-card">
            <div className="feature-icon">🛣️</div>
            <h3>React Router DOM</h3>
            <p>Navigate between multiple pages with client-side routing</p>
            <ul>
              <li>BrowserRouter setup</li>
              <li>Route configuration</li>
              <li>Dynamic routes with parameters</li>
              <li>Navigation with Link components</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">🟢</div>
            <h3>Node.js Backend</h3>
            <p>RESTful API server with Express.js</p>
            <ul>
              <li>Express server setup</li>
              <li>API endpoints</li>
              <li>CORS configuration</li>
              <li>Request/Response handling</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">🔗</div>
            <h3>Frontend-Backend Integration</h3>
            <p>Connect React with Node.js API</p>
            <ul>
              <li>Axios for HTTP requests</li>
              <li>Async/await patterns</li>
              <li>Error handling</li>
              <li>Data fetching in components</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Quick Navigation */}
      <section className="quick-nav">
        <h2>🧭 Explore the App</h2>
        <div className="nav-cards">
          <Link to="/about" className="nav-card">
            <div className="nav-icon">ℹ️</div>
            <h3>About</h3>
            <p>Learn about this project and its architecture</p>
          </Link>

          <Link to="/products" className="nav-card">
            <div className="nav-icon">📦</div>
            <h3>Products</h3>
            <p>Browse products with filtering and detailed views</p>
          </Link>

          <Link to="/users" className="nav-card">
            <div className="nav-icon">👥</div>
            <h3>Users</h3>
            <p>View user profiles and detailed information</p>
          </Link>

          <Link to="/contact" className="nav-card">
            <div className="nav-icon">📧</div>
            <h3>Contact</h3>
            <p>Send messages through our contact form</p>
          </Link>
        </div>
      </section>

      {/* Technology Stack */}
      <section className="tech-stack">
        <h2>🛠️ Technology Stack</h2>
        <div className="tech-grid">
          <div className="tech-item">
            <strong>Frontend:</strong>
            <span>React 18, React Router DOM, Axios, Webpack</span>
          </div>
          <div className="tech-item">
            <strong>Backend:</strong>
            <span>Node.js, Express.js, CORS, Body Parser</span>
          </div>
          <div className="tech-item">
            <strong>Development:</strong>
            <span>Nodemon, Webpack Dev Server, Concurrently</span>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Home;

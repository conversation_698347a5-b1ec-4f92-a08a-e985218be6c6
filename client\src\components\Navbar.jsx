import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  // Function to check if current route is active
  const isActiveRoute = (path) => {
    if (path === '/' && location.pathname === '/') {
      return true;
    }
    return path !== '/' && location.pathname.startsWith(path);
  };

  // Toggle mobile menu
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Close menu when link is clicked (for mobile)
  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <nav className="navbar">
      <div className="nav-container">
        {/* Logo/Brand */}
        <Link to="/" className="nav-logo" onClick={closeMenu}>
          ⚡ Vite React Router
        </Link>

        {/* Mobile menu button */}
        <button 
          className={`nav-toggle ${isMenuOpen ? 'active' : ''}`}
          onClick={toggleMenu}
          aria-label="Toggle navigation menu"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>

        {/* Navigation menu */}
        <ul className={`nav-menu ${isMenuOpen ? 'active' : ''}`}>
          <li className="nav-item">
            <Link 
              to="/" 
              className={`nav-link ${isActiveRoute('/') ? 'active' : ''}`}
              onClick={closeMenu}
            >
              🏠 Home
            </Link>
          </li>
          
          <li className="nav-item">
            <Link 
              to="/about" 
              className={`nav-link ${isActiveRoute('/about') ? 'active' : ''}`}
              onClick={closeMenu}
            >
              ℹ️ About
            </Link>
          </li>
          
          <li className="nav-item">
            <Link 
              to="/products" 
              className={`nav-link ${isActiveRoute('/products') ? 'active' : ''}`}
              onClick={closeMenu}
            >
              📦 Products
            </Link>
          </li>
          
          <li className="nav-item">
            <Link 
              to="/users" 
              className={`nav-link ${isActiveRoute('/users') ? 'active' : ''}`}
              onClick={closeMenu}
            >
              👥 Users
            </Link>
          </li>
          
          <li className="nav-item">
            <Link 
              to="/contact" 
              className={`nav-link ${isActiveRoute('/contact') ? 'active' : ''}`}
              onClick={closeMenu}
            >
              📧 Contact
            </Link>
          </li>
        </ul>
      </div>
    </nav>
  );
}

export default Navbar;

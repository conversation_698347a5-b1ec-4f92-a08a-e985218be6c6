import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Home from './pages/Home';
import About from './pages/About';
import Products from './pages/Products';
import ProductDetail from './pages/ProductDetail';
import Users from './pages/Users';
import UserProfile from './pages/UserProfile';
import Contact from './pages/Contact';
import NotFound from './pages/NotFound';
import './App.css';

function App() {
  return (
    <Router>
      <div className="app">
        <Routes>
          {/* All routes wrapped in Layout component */}
          <Route path="/" element={<Layout />}>
            {/* Home route */}
            <Route index element={<Home />} />
            
            {/* About route */}
            <Route path="about" element={<About />} />
            
            {/* Products routes */}
            <Route path="products" element={<Products />} />
            <Route path="products/:id" element={<ProductDetail />} />
            
            {/* Users routes */}
            <Route path="users" element={<Users />} />
            <Route path="users/:id" element={<UserProfile />} />
            
            {/* Contact route */}
            <Route path="contact" element={<Contact />} />
            
            {/* 404 Not Found route - must be last */}
            <Route path="*" element={<NotFound />} />
          </Route>
        </Routes>
      </div>
    </Router>
  );
}

export default App;

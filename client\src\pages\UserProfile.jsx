import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';

function UserProfile() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchUser();
  }, [id]);

  const fetchUser = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/users/${id}`);
      setUser(response.data.data);
      setError(null);
    } catch (err) {
      setError(err.response?.status === 404 ? 'User not found' : 'Failed to fetch user');
      console.error('Error fetching user:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="page user-profile-page">
        <div className="loading">
          <div className="spinner"></div>
          <p>Loading user profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page user-profile-page">
        <div className="error">
          <h2>❌ Error</h2>
          <p>{error}</p>
          <div className="error-actions">
            <button onClick={fetchUser} className="retry-button">
              Try Again
            </button>
            <Link to="/users" className="back-button">
              Back to Users
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page user-profile-page">
      {/* Breadcrumb */}
      <nav className="breadcrumb">
        <Link to="/">Home</Link>
        <span>/</span>
        <Link to="/users">Users</Link>
        <span>/</span>
        <span>{user.name}</span>
      </nav>

      {/* User Profile */}
      <section className="user-profile">
        <div className="profile-header">
          <div className="profile-avatar-large">
            <span className="avatar-icon-large">{user.avatar}</span>
          </div>
          
          <div className="profile-info">
            <h1>{user.name}</h1>
            <p className="profile-email">{user.email}</p>
            <span className={`profile-role role-${user.role.toLowerCase()}`}>
              {user.role}
            </span>
          </div>
        </div>

        <div className="profile-details">
          <div className="detail-section">
            <h3>Contact Information</h3>
            <div className="detail-grid">
              <div className="detail-item">
                <strong>Email:</strong>
                <span>{user.email}</span>
              </div>
              <div className="detail-item">
                <strong>User ID:</strong>
                <span>#{user.id}</span>
              </div>
              <div className="detail-item">
                <strong>Role:</strong>
                <span>{user.role}</span>
              </div>
            </div>
          </div>

          <div className="detail-section">
            <h3>Profile Actions</h3>
            <div className="profile-actions">
              <button className="action-btn primary">
                📧 Send Message
              </button>
              <button className="action-btn secondary">
                📞 Schedule Call
              </button>
              <button className="action-btn tertiary">
                👥 Add to Team
              </button>
            </div>
          </div>

          <div className="detail-section">
            <h3>Recent Activity</h3>
            <div className="activity-list">
              <div className="activity-item">
                <span className="activity-icon">✅</span>
                <span className="activity-text">Completed project review</span>
                <span className="activity-time">2 hours ago</span>
              </div>
              <div className="activity-item">
                <span className="activity-icon">📝</span>
                <span className="activity-text">Updated profile information</span>
                <span className="activity-time">1 day ago</span>
              </div>
              <div className="activity-item">
                <span className="activity-icon">🎯</span>
                <span className="activity-text">Joined new project team</span>
                <span className="activity-time">3 days ago</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Navigation */}
      <section className="profile-navigation">
        <button onClick={() => navigate(-1)} className="back-btn">
          ← Go Back
        </button>
        <Link to="/users" className="all-users-btn">
          View All Users
        </Link>
      </section>

      {/* Demo Info */}
      <section className="demo-info">
        <h2>🎯 React Router Features</h2>
        <div className="demo-features">
          <div className="demo-feature">
            <h3>useParams Hook</h3>
            <p>Extracting user ID ({id}) from URL parameters</p>
          </div>
          <div className="demo-feature">
            <h3>useNavigate Hook</h3>
            <p>Programmatic navigation with history support</p>
          </div>
          <div className="demo-feature">
            <h3>Nested Routing</h3>
            <p>User profile as a child route of the users section</p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default UserProfile;

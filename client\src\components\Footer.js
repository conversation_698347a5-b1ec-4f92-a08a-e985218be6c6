import React from 'react';
import { Link } from 'react-router-dom';

function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="footer">
      <div className="footer-container">
        <div className="footer-content">
          {/* Brand section */}
          <div className="footer-section">
            <h3>🚀 React Router Demo</h3>
            <p>A full-stack application demonstrating React Router DOM with Node.js backend.</p>
          </div>

          {/* Quick links */}
          <div className="footer-section">
            <h4>Quick Links</h4>
            <ul className="footer-links">
              <li><Link to="/">Home</Link></li>
              <li><Link to="/about">About</Link></li>
              <li><Link to="/products">Products</Link></li>
              <li><Link to="/users">Users</Link></li>
              <li><Link to="/contact">Contact</Link></li>
            </ul>
          </div>

          {/* Technologies */}
          <div className="footer-section">
            <h4>Technologies Used</h4>
            <ul className="footer-tech">
              <li>⚛️ React 18</li>
              <li>🛣️ React Router DOM</li>
              <li>🟢 Node.js</li>
              <li>🚀 Express.js</li>
              <li>📦 Webpack</li>
            </ul>
          </div>

          {/* Contact info */}
          <div className="footer-section">
            <h4>Connect</h4>
            <div className="footer-contact">
              <p>📧 <EMAIL></p>
              <p>🌐 localhost:3000</p>
              <p>🔗 API: localhost:5000</p>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="footer-bottom">
          <p>&copy; {currentYear} React Router Demo. Built for learning purposes.</p>
          <p>Made with ❤️ using React Router DOM & Node.js</p>
        </div>
      </div>
    </footer>
  );
}

export default Footer;

import React, { useState } from 'react';
import axios from 'axios';

function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post('/api/contact', formData);
      setSuccess(true);
      setFormData({ name: '', email: '', subject: '', message: '' });
      console.log('Contact form submitted:', response.data);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to send message');
      console.error('Error submitting contact form:', err);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSuccess(false);
    setError('');
  };

  return (
    <div className="page contact-page">
      {/* Header */}
      <section className="page-header">
        <h1>📧 Contact Us</h1>
        <p>Get in touch with us. We'd love to hear from you!</p>
      </section>

      <div className="contact-container">
        {/* Contact Form */}
        <section className="contact-form-section">
          <h2>Send us a Message</h2>
          
          {success ? (
            <div className="success-message">
              <div className="success-icon">✅</div>
              <h3>Message Sent Successfully!</h3>
              <p>Thank you for contacting us. We'll get back to you soon.</p>
              <button onClick={resetForm} className="send-another-btn">
                Send Another Message
              </button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="contact-form">
              <div className="form-group">
                <label htmlFor="name">Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  placeholder="Your full name"
                />
              </div>

              <div className="form-group">
                <label htmlFor="email">Email *</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="form-group">
                <label htmlFor="subject">Subject *</label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  required
                  placeholder="What is this about?"
                />
              </div>

              <div className="form-group">
                <label htmlFor="message">Message *</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows="6"
                  placeholder="Tell us more about your inquiry..."
                ></textarea>
              </div>

              {error && (
                <div className="error-message">
                  <span className="error-icon">❌</span>
                  {error}
                </div>
              )}

              <button 
                type="submit" 
                className="submit-btn"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="loading-spinner"></span>
                    Sending...
                  </>
                ) : (
                  <>
                    📤 Send Message
                  </>
                )}
              </button>
            </form>
          )}
        </section>

        {/* Contact Info */}
        <section className="contact-info-section">
          <h2>Get in Touch</h2>
          
          <div className="contact-methods">
            <div className="contact-method">
              <div className="method-icon">📧</div>
              <h3>Email</h3>
              <p><EMAIL></p>
              <p>We'll respond within 24 hours</p>
            </div>

            <div className="contact-method">
              <div className="method-icon">📞</div>
              <h3>Phone</h3>
              <p>+****************</p>
              <p>Mon-Fri, 9AM-5PM EST</p>
            </div>

            <div className="contact-method">
              <div className="method-icon">💬</div>
              <h3>Live Chat</h3>
              <p>Available on our website</p>
              <p>Instant support during business hours</p>
            </div>

            <div className="contact-method">
              <div className="method-icon">📍</div>
              <h3>Office</h3>
              <p>123 Demo Street</p>
              <p>Tech City, TC 12345</p>
            </div>
          </div>

          <div className="social-links">
            <h3>Follow Us</h3>
            <div className="social-icons">
              <a href="#" className="social-link">🐦 Twitter</a>
              <a href="#" className="social-link">📘 Facebook</a>
              <a href="#" className="social-link">💼 LinkedIn</a>
              <a href="#" className="social-link">📷 Instagram</a>
            </div>
          </div>
        </section>
      </div>

      {/* Demo Info */}
      <section className="demo-info">
        <h2>🎯 Features Demonstrated</h2>
        <div className="demo-features">
          <div className="demo-feature">
            <h3>📤 Form Submission</h3>
            <p>POST request to Node.js API with form validation</p>
          </div>
          <div className="demo-feature">
            <h3>🔄 State Management</h3>
            <p>React hooks for form state, loading, and success states</p>
          </div>
          <div className="demo-feature">
            <h3>⚡ Vite Development</h3>
            <p>Instant feedback during form development with HMR</p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Contact;

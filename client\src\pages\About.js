import React from 'react';
import { Link } from 'react-router-dom';

function About() {
  return (
    <div className="page about-page">
      {/* Header */}
      <section className="page-header">
        <h1>ℹ️ About This Project</h1>
        <p>Learn about the architecture and features of this React Router + Node.js application</p>
      </section>

      {/* Project Overview */}
      <section className="content-section">
        <h2>🎯 Project Overview</h2>
        <p>
          This is a comprehensive full-stack web application that demonstrates the integration 
          of React Router DOM for client-side routing with a Node.js Express server for backend API services.
        </p>
        
        <div className="overview-grid">
          <div className="overview-item">
            <h3>🎨 Frontend Features</h3>
            <ul>
              <li>Multiple pages with React Router DOM</li>
              <li>Dynamic routing with URL parameters</li>
              <li>Responsive navigation with active states</li>
              <li>API integration with Axios</li>
              <li>Modern React hooks (useState, useEffect)</li>
              <li>Mobile-responsive design</li>
            </ul>
          </div>

          <div className="overview-item">
            <h3>⚙️ Backend Features</h3>
            <ul>
              <li>RESTful API with Express.js</li>
              <li>CORS enabled for cross-origin requests</li>
              <li>Multiple endpoints for different data types</li>
              <li>Request validation and error handling</li>
              <li>Environment configuration</li>
              <li>Health check endpoint</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Architecture */}
      <section className="content-section">
        <h2>🏗️ Architecture</h2>
        <div className="architecture-diagram">
          <div className="arch-layer">
            <h3>Frontend (React)</h3>
            <div className="arch-components">
              <span>React Router</span>
              <span>Components</span>
              <span>Pages</span>
              <span>Axios</span>
            </div>
          </div>
          <div className="arch-arrow">⬇️</div>
          <div className="arch-layer">
            <h3>HTTP Requests</h3>
            <div className="arch-components">
              <span>GET /api/users</span>
              <span>GET /api/products</span>
              <span>POST /api/contact</span>
            </div>
          </div>
          <div className="arch-arrow">⬇️</div>
          <div className="arch-layer">
            <h3>Backend (Node.js)</h3>
            <div className="arch-components">
              <span>Express Server</span>
              <span>API Routes</span>
              <span>Data Layer</span>
              <span>CORS</span>
            </div>
          </div>
        </div>
      </section>

      {/* React Router Concepts */}
      <section className="content-section">
        <h2>🛣️ React Router Concepts Demonstrated</h2>
        <div className="concepts-grid">
          <div className="concept-card">
            <h3>BrowserRouter</h3>
            <p>Wraps the entire application to enable routing functionality</p>
            <code>&lt;BrowserRouter&gt;</code>
          </div>

          <div className="concept-card">
            <h3>Routes & Route</h3>
            <p>Define which component to render for each URL path</p>
            <code>&lt;Route path="/about" element={&lt;About /&gt;} /&gt;</code>
          </div>

          <div className="concept-card">
            <h3>Link Navigation</h3>
            <p>Navigate between pages without full page reload</p>
            <code>&lt;Link to="/products"&gt;Products&lt;/Link&gt;</code>
          </div>

          <div className="concept-card">
            <h3>Dynamic Routes</h3>
            <p>Routes with parameters for dynamic content</p>
            <code>&lt;Route path="/users/:id" element={&lt;UserProfile /&gt;} /&gt;</code>
          </div>

          <div className="concept-card">
            <h3>useLocation Hook</h3>
            <p>Access current location for active navigation states</p>
            <code>const location = useLocation();</code>
          </div>

          <div className="concept-card">
            <h3>useParams Hook</h3>
            <p>Extract parameters from the current route</p>
            <code>const { id } = useParams();</code>
          </div>
        </div>
      </section>

      {/* API Endpoints */}
      <section className="content-section">
        <h2>🔗 API Endpoints</h2>
        <div className="api-table">
          <div className="api-row header">
            <span>Method</span>
            <span>Endpoint</span>
            <span>Description</span>
          </div>
          <div className="api-row">
            <span className="method get">GET</span>
            <span>/api/users</span>
            <span>Get all users</span>
          </div>
          <div className="api-row">
            <span className="method get">GET</span>
            <span>/api/users/:id</span>
            <span>Get user by ID</span>
          </div>
          <div className="api-row">
            <span className="method get">GET</span>
            <span>/api/products</span>
            <span>Get all products</span>
          </div>
          <div className="api-row">
            <span className="method get">GET</span>
            <span>/api/products/:id</span>
            <span>Get product by ID</span>
          </div>
          <div className="api-row">
            <span className="method post">POST</span>
            <span>/api/contact</span>
            <span>Submit contact form</span>
          </div>
          <div className="api-row">
            <span className="method get">GET</span>
            <span>/api/health</span>
            <span>Health check</span>
          </div>
        </div>
      </section>

      {/* Getting Started */}
      <section className="content-section">
        <h2>🚀 Getting Started</h2>
        <div className="getting-started">
          <div className="step">
            <h3>1. Install Dependencies</h3>
            <code>npm run install-all</code>
          </div>
          <div className="step">
            <h3>2. Start Development</h3>
            <code>npm run dev</code>
          </div>
          <div className="step">
            <h3>3. Explore the App</h3>
            <p>Visit different pages and see how routing works!</p>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="cta-section">
        <h2>Ready to Explore?</h2>
        <p>Check out the different pages to see React Router in action!</p>
        <div className="cta-buttons">
          <Link to="/products" className="cta-button primary">
            View Products
          </Link>
          <Link to="/users" className="cta-button secondary">
            Browse Users
          </Link>
        </div>
      </section>
    </div>
  );
}

export default About;

import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Home from './pages/Home';
import About from './pages/About';
import Products from './pages/Products';
import ProductDetail from './pages/ProductDetail';
import Users from './pages/Users';
import UserProfile from './pages/UserProfile';
import Contact from './pages/Contact';
import NotFound from './pages/NotFound';
import './App.css';

function App() {
  return (
    <Router>
      <div className="app">
        {/* Navigation component - appears on all pages */}
        <Navbar />
        
        {/* Main content area */}
        <main className="main-content">
          <Routes>
            {/* Home route */}
            <Route path="/" element={<Home />} />
            
            {/* About route */}
            <Route path="/about" element={<About />} />
            
            {/* Products routes */}
            <Route path="/products" element={<Products />} />
            <Route path="/products/:id" element={<ProductDetail />} />
            
            {/* Users routes */}
            <Route path="/users" element={<Users />} />
            <Route path="/users/:id" element={<UserProfile />} />
            
            {/* Contact route */}
            <Route path="/contact" element={<Contact />} />
            
            {/* 404 Not Found route - must be last */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
        
        {/* Footer component - appears on all pages */}
        <Footer />
      </div>
    </Router>
  );
}

export default App;

import React from 'react';
import { Link } from 'react-router-dom';

function About() {
  return (
    <div className="page about-page">
      {/* Header */}
      <section className="page-header">
        <h1>ℹ️ About This Project</h1>
        <p>Learn about the modern architecture of this Vite + React Router + Node.js application</p>
      </section>

      {/* Project Overview */}
      <section className="content-section">
        <h2>🎯 Project Overview</h2>
        <p>
          This is a modern full-stack web application that demonstrates the power of combining 
          Vite's lightning-fast development experience with React Router DOM for client-side routing 
          and a Node.js Express server for backend API services.
        </p>
        
        <div className="overview-grid">
          <div className="overview-item">
            <h3>🎨 Frontend Features</h3>
            <ul>
              <li>⚡ Vite for instant development server</li>
              <li>🛣️ React Router DOM v6 with nested routes</li>
              <li>📱 Responsive design with modern CSS</li>
              <li>🔗 API integration with Axios</li>
              <li>🎣 Modern React hooks (useState, useEffect, useParams)</li>
              <li>🎯 Component-based architecture</li>
            </ul>
          </div>

          <div className="overview-item">
            <h3>⚙️ Backend Features</h3>
            <ul>
              <li>🟢 Node.js with ES modules</li>
              <li>🚀 Express.js RESTful API</li>
              <li>🌐 CORS enabled for cross-origin requests</li>
              <li>📊 Multiple data endpoints</li>
              <li>✅ Request validation and error handling</li>
              <li>🔍 Health check and monitoring</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Vite Benefits */}
      <section className="content-section">
        <h2>⚡ Why Vite?</h2>
        <div className="vite-benefits">
          <div className="benefit-card">
            <h3>🚀 Instant Server Start</h3>
            <p>Vite starts the dev server instantly, regardless of app size</p>
          </div>
          <div className="benefit-card">
            <h3>⚡ Lightning Fast HMR</h3>
            <p>Hot Module Replacement that stays fast regardless of app size</p>
          </div>
          <div className="benefit-card">
            <h3>📦 Optimized Builds</h3>
            <p>Pre-configured Rollup build with multi-page and library mode support</p>
          </div>
          <div className="benefit-card">
            <h3>🔧 Rich Features</h3>
            <p>TypeScript, JSX, CSS and more, out of the box</p>
          </div>
        </div>
      </section>

      {/* React Router Concepts */}
      <section className="content-section">
        <h2>🛣️ React Router Concepts</h2>
        <div className="router-concepts">
          <div className="concept-card">
            <h3>BrowserRouter</h3>
            <p>Uses HTML5 history API to keep UI in sync with URL</p>
            <code>&lt;BrowserRouter&gt;&lt;App /&gt;&lt;/BrowserRouter&gt;</code>
          </div>

          <div className="concept-card">
            <h3>Nested Routes</h3>
            <p>Routes can be nested inside other routes using Outlet</p>
            <code>&lt;Route path="/" element={&lt;Layout /&gt;}&gt;</code>
          </div>

          <div className="concept-card">
            <h3>Dynamic Routes</h3>
            <p>Routes with parameters for dynamic content</p>
            <code>&lt;Route path="/users/:id" element={&lt;UserProfile /&gt;} /&gt;</code>
          </div>

          <div className="concept-card">
            <h3>Navigation Hooks</h3>
            <p>Hooks for programmatic navigation and route information</p>
            <code>useNavigate(), useParams(), useLocation()</code>
          </div>
        </div>
      </section>

      {/* Architecture Diagram */}
      <section className="content-section">
        <h2>🏗️ Application Architecture</h2>
        <div className="architecture">
          <div className="arch-layer frontend">
            <h3>Frontend (Vite + React)</h3>
            <div className="arch-components">
              <span>React Router</span>
              <span>Components</span>
              <span>Pages</span>
              <span>Hooks</span>
            </div>
          </div>
          
          <div className="arch-connection">
            <div className="connection-line"></div>
            <span className="connection-label">HTTP Requests (Axios)</span>
          </div>
          
          <div className="arch-layer backend">
            <h3>Backend (Node.js + Express)</h3>
            <div className="arch-components">
              <span>API Routes</span>
              <span>Middleware</span>
              <span>Data Layer</span>
              <span>CORS</span>
            </div>
          </div>
        </div>
      </section>

      {/* API Documentation */}
      <section className="content-section">
        <h2>📡 API Endpoints</h2>
        <div className="api-docs">
          <div className="endpoint-group">
            <h3>Users</h3>
            <div className="endpoint">
              <span className="method get">GET</span>
              <span className="path">/api/users</span>
              <span className="description">Get all users with optional role filter</span>
            </div>
            <div className="endpoint">
              <span className="method get">GET</span>
              <span className="path">/api/users/:id</span>
              <span className="description">Get specific user by ID</span>
            </div>
          </div>

          <div className="endpoint-group">
            <h3>Products</h3>
            <div className="endpoint">
              <span className="method get">GET</span>
              <span className="path">/api/products</span>
              <span className="description">Get all products with filtering options</span>
            </div>
            <div className="endpoint">
              <span className="method get">GET</span>
              <span className="path">/api/products/:id</span>
              <span className="description">Get specific product by ID</span>
            </div>
          </div>

          <div className="endpoint-group">
            <h3>Contact</h3>
            <div className="endpoint">
              <span className="method post">POST</span>
              <span className="path">/api/contact</span>
              <span className="description">Submit contact form</span>
            </div>
            <div className="endpoint">
              <span className="method get">GET</span>
              <span className="path">/api/contacts</span>
              <span className="description">Get all contact submissions</span>
            </div>
          </div>

          <div className="endpoint-group">
            <h3>System</h3>
            <div className="endpoint">
              <span className="method get">GET</span>
              <span className="path">/api/health</span>
              <span className="description">Health check endpoint</span>
            </div>
            <div className="endpoint">
              <span className="method get">GET</span>
              <span className="path">/api/stats</span>
              <span className="description">Get application statistics</span>
            </div>
          </div>
        </div>
      </section>

      {/* Development Setup */}
      <section className="content-section">
        <h2>🛠️ Development Setup</h2>
        <div className="setup-steps">
          <div className="setup-step">
            <h3>1. Project Structure</h3>
            <pre><code>{`project/
├── client/          # Vite React app
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── App.jsx
│   └── vite.config.js
├── server/          # Node.js Express API
│   ├── index.js
│   └── package.json
└── package.json     # Root scripts`}</code></pre>
          </div>

          <div className="setup-step">
            <h3>2. Installation</h3>
            <pre><code>{`# Install all dependencies
npm run install-all

# Or install separately
npm run install-client
npm run install-server`}</code></pre>
          </div>

          <div className="setup-step">
            <h3>3. Development</h3>
            <pre><code>{`# Start both client and server
npm run dev

# Or start separately
npm run client  # Vite dev server on :3000
npm run server  # Express server on :5000`}</code></pre>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="cta-section">
        <h2>Ready to Explore?</h2>
        <p>Navigate through the different pages to see React Router and API integration in action!</p>
        <div className="cta-buttons">
          <Link to="/products" className="cta-button primary">
            Browse Products
          </Link>
          <Link to="/users" className="cta-button secondary">
            View Users
          </Link>
          <Link to="/contact" className="cta-button tertiary">
            Contact Form
          </Link>
        </div>
      </section>
    </div>
  );
}

export default About;

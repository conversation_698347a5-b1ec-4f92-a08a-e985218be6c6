/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 80px; /* Account for fixed navbar */
}

/* Page Styles */
.page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-lg);
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
  padding: var(--spacing-xl) 0;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-hover)
  );
  color: var(--text-white);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-xxl);
}

.page-header h1 {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header p {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* Navbar Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  height: 70px;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.nav-logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-lg);
  margin: 0;
  padding: 0;
}

.nav-link {
  color: var(--text-primary);
  text-decoration: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-weight: 500;
}

.nav-link:hover {
  background: var(--bg-secondary);
  color: var(--primary-color);
}

.nav-link.active {
  background: var(--primary-color);
  color: var(--text-white);
}

/* Mobile Navigation */
.nav-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  margin: 3px 0;
  transition: var(--transition-fast);
}

@media (max-width: 768px) {
  .nav-toggle {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: var(--bg-primary);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: var(--spacing-xl);
    transition: left var(--transition-normal);
    box-shadow: var(--shadow-lg);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-link {
    padding: var(--spacing-lg);
    font-size: 1.2rem;
    width: 200px;
    text-align: center;
  }
}

/* Footer Styles */
.footer {
  background: var(--text-primary);
  color: var(--text-white);
  padding: var(--spacing-xxl) 0 var(--spacing-lg);
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-white);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: #ccc;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--text-white);
}

.footer-tech {
  list-style: none;
}

.footer-tech li {
  margin-bottom: var(--spacing-sm);
  color: #ccc;
}

.footer-contact p {
  margin-bottom: var(--spacing-sm);
  color: #ccc;
}

.footer-bottom {
  border-top: 1px solid #444;
  padding-top: var(--spacing-lg);
  text-align: center;
  color: #ccc;
}

.footer-bottom p {
  margin-bottom: var(--spacing-sm);
}

/* Home Page Styles */
.hero {
  text-align: center;
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--text-white);
  border-radius: var(--radius-xl);
  margin-bottom: var(--spacing-xxl);
}

.hero h1 {
  font-size: 3.5rem;
  margin-bottom: var(--spacing-lg);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: var(--spacing-sm);
}

.stat-label {
  font-size: 1rem;
  opacity: 0.8;
}

.api-status {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  display: inline-block;
  backdrop-filter: blur(10px);
}

/* Features Grid */
.features {
  margin-bottom: var(--spacing-xxl);
}

.features h2 {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  font-size: 2.5rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.feature-card {
  background: var(--bg-primary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  text-align: center;
  transition: transform var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
}

.feature-card h3 {
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

.feature-card ul {
  text-align: left;
  list-style: none;
  padding-left: 0;
}

.feature-card li {
  padding: var(--spacing-xs) 0;
  position: relative;
  padding-left: var(--spacing-lg);
}

.feature-card li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: bold;
}

/* Quick Navigation Cards */
.quick-nav {
  margin-bottom: var(--spacing-xxl);
}

.quick-nav h2 {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  font-size: 2.5rem;
}

.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.nav-card {
  background: var(--bg-primary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  text-decoration: none;
  color: var(--text-primary);
  transition: all var(--transition-normal);
  text-align: center;
  border: 2px solid transparent;
}

.nav-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
  color: var(--text-primary);
}

.nav-icon {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
}

.nav-card h3 {
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

/* Technology Grid */
.tech-highlights {
  margin-bottom: var(--spacing-xxl);
}

.tech-highlights h2 {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  font-size: 2.5rem;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.tech-item {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  text-align: center;
  transition: transform var(--transition-normal);
}

.tech-item:hover {
  transform: translateY(-3px);
}

.tech-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.tech-item h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
}

/* Getting Started Steps */
.getting-started {
  background: var(--bg-secondary);
  padding: var(--spacing-xxl);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-xxl);
}

.getting-started h2 {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  font-size: 2.5rem;
}

.steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.step {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.step-number {
  background: var(--primary-color);
  color: var(--text-white);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content h3 {
  margin-bottom: var(--spacing-sm);
}

.step-content code {
  background: var(--bg-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  font-family: "Courier New", monospace;
  display: block;
  margin-top: var(--spacing-sm);
}

/* Products Page Styles */
.filters-section {
  background: var(--bg-secondary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-xl);
}

.filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.filter-group label {
  font-weight: 600;
  color: var(--text-primary);
}

.clear-filters {
  background: var(--error-color);
  color: var(--text-white);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background var(--transition-fast);
  align-self: flex-end;
}

.clear-filters:hover {
  background: #e55555;
}

.results-info {
  text-align: center;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xxl);
}

.product-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.product-image {
  background: var(--bg-secondary);
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-icon {
  font-size: 4rem;
}

.product-info {
  padding: var(--spacing-lg);
}

.product-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.product-category {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-sm);
}

.product-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin-bottom: var(--spacing-md);
}

.product-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.product-stock {
  margin-bottom: var(--spacing-lg);
}

.in-stock {
  color: var(--success-color);
  font-weight: 500;
}

.out-of-stock {
  color: var(--error-color);
  font-weight: 500;
}

.product-actions {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.view-details-btn {
  background: var(--primary-color);
  color: var(--text-white);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  text-decoration: none;
  display: inline-block;
  transition: background var(--transition-fast);
  width: 100%;
  text-align: center;
}

.view-details-btn:hover {
  background: var(--primary-hover);
  color: var(--text-white);
}

/* Contact Page Styles */
.contact-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xxl);
  margin-bottom: var(--spacing-xxl);
}

.contact-form-section {
  background: var(--bg-primary);
  padding: var(--spacing-xxl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.contact-form {
  max-width: 500px;
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid #ddd;
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.submit-btn {
  background: var(--primary-color);
  color: var(--text-white);
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-md);
  font-size: 1.1rem;
  cursor: pointer;
  transition: background var(--transition-fast);
  width: 100%;
}

.submit-btn:hover:not(:disabled) {
  background: var(--primary-hover);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.success-message {
  text-align: center;
  padding: var(--spacing-xxl);
}

.success-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
}

.success-message h3 {
  color: var(--success-color);
  margin-bottom: var(--spacing-md);
}

.send-another-btn {
  background: var(--primary-color);
  color: var(--text-white);
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  margin-top: var(--spacing-lg);
}

.error-message {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  color: #c53030;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.contact-info-section {
  background: var(--bg-secondary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
}

.contact-methods {
  margin-bottom: var(--spacing-xl);
}

.contact-method {
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.method-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.contact-method h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
}

.social-links {
  text-align: center;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.social-link {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.social-link:hover {
  background: var(--primary-color);
  color: var(--text-white);
}

/* Users Page Styles */
.role-filters {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  justify-content: center;
}

.role-filter {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 2px solid #ddd;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.role-filter:hover {
  border-color: var(--primary-color);
}

.role-filter.active {
  background: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xxl);
}

.user-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all var(--transition-normal);
}

.user-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.user-avatar {
  margin-bottom: var(--spacing-lg);
}

.avatar-icon {
  font-size: 4rem;
}

.user-name {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.user-email {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.user-role {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: var(--spacing-lg);
}

.role-admin {
  background: #e3f2fd;
  color: #1565c0;
}
.role-developer {
  background: #f3e5f5;
  color: #7b1fa2;
}
.role-designer {
  background: #fff3e0;
  color: #ef6c00;
}
.role-manager {
  background: #e8f5e8;
  color: #2e7d32;
}

.view-profile-btn {
  background: var(--primary-color);
  color: var(--text-white);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: background var(--transition-fast);
}

.view-profile-btn:hover {
  background: var(--primary-hover);
  color: var(--text-white);
}

/* Demo Info Sections */
.demo-info {
  background: var(--bg-secondary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-xxl);
}

.demo-info h2 {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  color: var(--primary-color);
}

.demo-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.demo-feature {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  text-align: center;
}

.demo-feature h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
}

/* Breadcrumb */
.breadcrumb {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.breadcrumb a {
  color: var(--primary-color);
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.breadcrumb span {
  color: var(--text-secondary);
}

/* Product Detail Styles */
.product-detail {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-xxl);
  margin-bottom: var(--spacing-xxl);
}

.product-image-large {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-icon-large {
  font-size: 8rem;
}

.product-info-detailed h1 {
  margin-bottom: var(--spacing-md);
}

.product-category-badge {
  background: var(--primary-color);
  color: var(--text-white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  display: inline-block;
  margin-bottom: var(--spacing-lg);
}

.product-price-large {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.product-stock-info {
  margin-bottom: var(--spacing-xl);
}

.in-stock-large,
.out-of-stock-large {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.1rem;
  font-weight: 500;
}

.in-stock-large {
  color: var(--success-color);
}

.out-of-stock-large {
  color: var(--error-color);
}

.product-description-detailed {
  margin-bottom: var(--spacing-xl);
}

.product-description-detailed h3 {
  margin-bottom: var(--spacing-md);
}

.product-actions-detailed {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.add-to-cart-btn,
.wishlist-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-md);
  font-size: 1.1rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.add-to-cart-btn {
  background: var(--primary-color);
  color: var(--text-white);
  flex: 1;
}

.add-to-cart-btn:hover:not(:disabled) {
  background: var(--primary-hover);
}

.add-to-cart-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.wishlist-btn {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid #ddd;
}

.wishlist-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.product-meta {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
}

.meta-item {
  margin-bottom: var(--spacing-sm);
}

.meta-item:last-child {
  margin-bottom: 0;
}

/* User Profile Styles */
.user-profile {
  margin-bottom: var(--spacing-xxl);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xxl);
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

.profile-avatar-large {
  flex-shrink: 0;
}

.avatar-icon-large {
  font-size: 6rem;
}

.profile-info h1 {
  margin-bottom: var(--spacing-sm);
}

.profile-email {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-bottom: var(--spacing-md);
}

.profile-role {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 500;
}

.profile-details {
  display: grid;
  gap: var(--spacing-xl);
}

.detail-section {
  background: var(--bg-primary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.detail-section h3 {
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.detail-grid {
  display: grid;
  gap: var(--spacing-md);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid #eee;
}

.detail-item:last-child {
  border-bottom: none;
}

.profile-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.action-btn {
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.action-btn.primary {
  background: var(--primary-color);
  color: var(--text-white);
}

.action-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid #ddd;
}

.action-btn.tertiary {
  background: var(--success-color);
  color: var(--text-white);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.activity-icon {
  font-size: 1.2rem;
}

.activity-text {
  flex: 1;
}

.activity-time {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Navigation Buttons */
.product-navigation,
.profile-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xxl);
  gap: var(--spacing-md);
}

.back-btn,
.all-products-btn,
.all-users-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.back-btn {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid #ddd;
  cursor: pointer;
}

.back-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.all-products-btn,
.all-users-btn {
  background: var(--primary-color);
  color: var(--text-white);
}

.all-products-btn:hover,
.all-users-btn:hover {
  background: var(--primary-hover);
  color: var(--text-white);
}

/* Not Found Page */
.not-found-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xxl);
  align-items: center;
  min-height: 60vh;
}

.not-found-content {
  text-align: center;
}

.error-code {
  font-size: 8rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.not-found-content h1 {
  margin-bottom: var(--spacing-lg);
}

.error-message {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

.not-found-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-xl);
}

.home-btn,
.back-button {
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.home-btn {
  background: var(--primary-color);
  color: var(--text-white);
}

.home-btn:hover {
  background: var(--primary-hover);
  color: var(--text-white);
}

.back-button {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid #ddd;
}

.back-button:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.helpful-links {
  text-align: left;
}

.helpful-links ul {
  list-style: none;
  padding: 0;
}

.helpful-links li {
  margin-bottom: var(--spacing-sm);
}

.helpful-links a {
  color: var(--primary-color);
  text-decoration: none;
}

.helpful-links a:hover {
  text-decoration: underline;
}

.not-found-illustration {
  text-align: center;
}

.illustration {
  font-size: 6rem;
  margin-bottom: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-container {
    grid-template-columns: 1fr;
  }

  .product-detail {
    grid-template-columns: 1fr;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
  }

  .not-found-container {
    grid-template-columns: 1fr;
  }

  .product-actions-detailed {
    flex-direction: column;
  }

  .profile-actions {
    justify-content: center;
  }

  .not-found-actions {
    flex-direction: column;
    align-items: center;
  }
}

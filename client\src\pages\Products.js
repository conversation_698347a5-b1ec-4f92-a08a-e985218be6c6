import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

function Products() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    category: '',
    minPrice: '',
    maxPrice: ''
  });

  // Fetch products from API
  useEffect(() => {
    fetchProducts();
  }, [filters]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (filters.category) params.append('category', filters.category);
      if (filters.minPrice) params.append('minPrice', filters.minPrice);
      if (filters.maxPrice) params.append('maxPrice', filters.maxPrice);

      const response = await axios.get(`/api/products?${params}`);
      setProducts(response.data.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch products');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      category: '',
      minPrice: '',
      maxPrice: ''
    });
  };

  // Get unique categories
  const categories = [...new Set(products.map(product => product.category))];

  if (loading) {
    return (
      <div className="page products-page">
        <div className="loading">
          <div className="spinner"></div>
          <p>Loading products...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page products-page">
        <div className="error">
          <h2>❌ Error</h2>
          <p>{error}</p>
          <button onClick={fetchProducts} className="retry-button">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="page products-page">
      {/* Header */}
      <section className="page-header">
        <h1>📦 Products</h1>
        <p>Browse our collection of products with filtering options</p>
      </section>

      {/* Filters */}
      <section className="filters-section">
        <h2>🔍 Filter Products</h2>
        <div className="filters">
          <div className="filter-group">
            <label htmlFor="category">Category:</label>
            <select
              id="category"
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="minPrice">Min Price:</label>
            <input
              type="number"
              id="minPrice"
              placeholder="0"
              value={filters.minPrice}
              onChange={(e) => handleFilterChange('minPrice', e.target.value)}
            />
          </div>

          <div className="filter-group">
            <label htmlFor="maxPrice">Max Price:</label>
            <input
              type="number"
              id="maxPrice"
              placeholder="1000"
              value={filters.maxPrice}
              onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
            />
          </div>

          <button onClick={clearFilters} className="clear-filters">
            Clear Filters
          </button>
        </div>

        <div className="results-info">
          <p>Showing {products.length} product{products.length !== 1 ? 's' : ''}</p>
        </div>
      </section>

      {/* Products Grid */}
      <section className="products-grid">
        {products.length === 0 ? (
          <div className="no-products">
            <h3>No products found</h3>
            <p>Try adjusting your filters or check back later.</p>
          </div>
        ) : (
          products.map(product => (
            <div key={product.id} className="product-card">
              <div className="product-image">
                <div className="product-placeholder">
                  📦
                </div>
              </div>
              
              <div className="product-info">
                <h3 className="product-name">{product.name}</h3>
                <p className="product-category">{product.category}</p>
                <div className="product-price">
                  ${product.price.toFixed(2)}
                </div>
                <div className="product-stock">
                  {product.stock > 0 ? (
                    <span className="in-stock">✅ In Stock ({product.stock})</span>
                  ) : (
                    <span className="out-of-stock">❌ Out of Stock</span>
                  )}
                </div>
              </div>
              
              <div className="product-actions">
                <Link 
                  to={`/products/${product.id}`} 
                  className="view-details-btn"
                >
                  View Details
                </Link>
              </div>
            </div>
          ))
        )}
      </section>

      {/* Demo Info */}
      <section className="demo-info">
        <h2>🎯 React Router Features Demonstrated</h2>
        <div className="demo-features">
          <div className="demo-feature">
            <h3>Dynamic Routing</h3>
            <p>Click "View Details" to see dynamic routes with parameters (/products/:id)</p>
          </div>
          <div className="demo-feature">
            <h3>API Integration</h3>
            <p>Products are fetched from the Node.js backend API with filtering</p>
          </div>
          <div className="demo-feature">
            <h3>State Management</h3>
            <p>Filter state is managed with React hooks and updates the API calls</p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Products;

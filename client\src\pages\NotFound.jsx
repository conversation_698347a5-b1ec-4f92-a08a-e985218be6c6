import React from 'react';
import { Link, useNavigate } from 'react-router-dom';

function NotFound() {
  const navigate = useNavigate();

  return (
    <div className="page not-found-page">
      <div className="not-found-container">
        <div className="not-found-content">
          <div className="error-code">404</div>
          <h1>Page Not Found</h1>
          <p className="error-message">
            Oops! The page you're looking for doesn't exist or has been moved.
          </p>
          
          <div className="not-found-actions">
            <Link to="/" className="home-btn">
              🏠 Go Home
            </Link>
            <button onClick={() => navigate(-1)} className="back-btn">
              ← Go Back
            </button>
          </div>

          <div className="helpful-links">
            <h3>Try these instead:</h3>
            <ul>
              <li><Link to="/products">📦 Browse Products</Link></li>
              <li><Link to="/users">👥 View Users</Link></li>
              <li><Link to="/about">ℹ️ About This Project</Link></li>
              <li><Link to="/contact">📧 Contact Us</Link></li>
            </ul>
          </div>
        </div>

        <div className="not-found-illustration">
          <div className="illustration">
            🔍🚫
          </div>
          <p>Lost in the routes?</p>
        </div>
      </div>

      {/* Demo Info */}
      <section className="demo-info">
        <h2>🎯 React Router Feature</h2>
        <div className="demo-features">
          <div className="demo-feature">
            <h3>Catch-All Route</h3>
            <p>This page catches all unmatched routes using path="*"</p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default NotFound;
